package com.cosfo.mall.order.service.impl;

import cn.hutool.core.util.NumberUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.schedulerx.shade.com.google.common.collect.Lists;
import com.cosfo.mall.bill.model.dto.BillProfitSharingSnapshotDTO;
import com.cosfo.mall.bill.service.BillProfitSharingService;
import com.cosfo.mall.common.constant.NumberConstant;
import com.cosfo.mall.common.constants.ProfitSharingRuleTypeEnum;
import com.cosfo.mall.common.context.DeliveryTypeEnum;
import com.cosfo.mall.common.context.TenantTypeEnum;
import com.cosfo.mall.common.context.shard.AccountTypeEnum;
import com.cosfo.mall.common.context.shard.ProfitSharingDeliveryTypeEnums;
import com.cosfo.mall.common.utils.AssertCheckDefault;
import com.cosfo.mall.order.service.ProfitSharingCalculate;
import com.cosfo.mall.order.utils.CombinedPayAllocationCalculator;
import com.cosfo.mall.supplier.SupplierDeliveryInfoService;
import com.cosfo.mall.supplier.model.po.SupplierDeliveryInfo;
import com.cosfo.mall.tenant.model.dto.TenantDTO;
import com.cosfo.mall.tenant.service.TenantService;
import com.cosfo.ordercenter.client.common.OrderAfterSaleStatusEnum;
import com.cosfo.ordercenter.client.common.util.RpcResultUtil;
import com.cosfo.ordercenter.client.provider.OrderAfterSaleQueryProvider;
import com.cosfo.ordercenter.client.provider.OrderQueryProvider;
import com.cosfo.ordercenter.client.req.OrderAfterSaleQueryReq;
import com.cosfo.ordercenter.client.resp.aftersale.OrderAfterSaleResp;
import com.cosfo.ordercenter.client.resp.order.OrderResp;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static java.math.BigDecimal.ROUND_HALF_UP;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/10/23
 */
@Slf4j
@Service
public class DeliveryFeeProfitSharingCalculate implements ProfitSharingCalculate {

    @Resource
    private BillProfitSharingService billProfitSharingService;
    @Resource
    private TenantService tenantService;
    @Resource
    private SupplierDeliveryInfoService supplierDeliveryInfoService;
    @Resource
    private CombinedPayAllocationCalculator combinedPayAllocationCalculator;


    @DubboReference
    private OrderQueryProvider orderQueryProvider;
    @DubboReference
    private OrderAfterSaleQueryProvider orderAfterSaleQueryProvider;

    @Override
    public boolean support(Integer deliveryType, Integer profitSharingRuleType, Integer type) {
        return ProfitSharingRuleTypeEnum.DELIVERY.getCode().equals(profitSharingRuleType);
    }

    @Override
    public void profitSharingCalculate(List<BillProfitSharingSnapshotDTO> billProfitSharingSnapshotDtos, Map<Long, BigDecimal> accountProfitSharingPriceMap) {
        // 查询订单信息
        OrderResp orderDTO = RpcResultUtil.handle(orderQueryProvider.queryById(billProfitSharingSnapshotDtos.get(NumberConstant.ZERO).getOrderId()));
        // 查询已经完成的售后单
        OrderAfterSaleQueryReq afterSaleQueryReq = new OrderAfterSaleQueryReq();
        afterSaleQueryReq.setOrderIds(Lists.newArrayList(orderDTO.getId()));
        afterSaleQueryReq.setStatusList(Lists.newArrayList(OrderAfterSaleStatusEnum.AUDITED_SUCCESS.getValue()));
        List<OrderAfterSaleResp> afterSaleDTOList = RpcResultUtil.handle(orderAfterSaleQueryProvider.queryList(afterSaleQueryReq));

        BillProfitSharingSnapshotDTO billProfitSharingSnapshotDTO = billProfitSharingSnapshotDtos.get(NumberConstant.ZERO);
        // 计算无仓运费分账
        if (Objects.equals(ProfitSharingDeliveryTypeEnums.NO_WAREHOUSE.getType(), billProfitSharingSnapshotDTO.getDeliveryType())) {
            noWarehouseDeliveryFeeProfitSharingCalculate(billProfitSharingSnapshotDtos, orderDTO, afterSaleDTOList);
            return;
        }

        // 查询供应运费
        SupplierDeliveryInfo supplierDeliveryInfo = supplierDeliveryInfoService.querySupplierDeliveryInfo(orderDTO.getOrderNo());
        BigDecimal deliveryFee = orderDTO.getDeliveryFee(), supplierDeliveryFee = BigDecimal.ZERO, refundDeliveryFee = BigDecimal.ZERO;
        if (!CollectionUtils.isEmpty(afterSaleDTOList)) {
            for (OrderAfterSaleResp orderAfterSaleDto : afterSaleDTOList) {
                refundDeliveryFee = Objects.isNull(orderAfterSaleDto.getDeliveryFee()) ? refundDeliveryFee : NumberUtil.add(refundDeliveryFee, orderAfterSaleDto.getDeliveryFee());
            }
        }

        if (!Objects.isNull(supplierDeliveryInfo)) {
            supplierDeliveryFee = Objects.isNull(supplierDeliveryInfo.getSupplierDeliveryFee()) ? supplierDeliveryFee : supplierDeliveryInfo.getSupplierDeliveryFee();
        }

        // 剩余运费
        deliveryFee = NumberUtil.sub(deliveryFee, refundDeliveryFee);

        // 处理组合支付的分摊逻辑（仅对自营仓运费）
        if(DeliveryTypeEnum.BRAND_DELIVERY.getCode().equals(billProfitSharingSnapshotDTO.getDeliveryType())){
            CombinedPayAllocationCalculator.CombinedPayAllocationResult allocationResult =
                    combinedPayAllocationCalculator.calculateAllocation(orderDTO);
            BigDecimal deliveryAdjustment = allocationResult.getDeliveryAdjustment();

            // 从运费中减去组合支付分摊的金额
            deliveryFee = NumberUtil.sub(deliveryFee, deliveryAdjustment);

            log.info("订单[{}]自营仓运费分账: 原运费调整后={}, 组合支付运费调整金额={}",
                    orderDTO.getId(), deliveryFee, deliveryAdjustment);
        }

        TenantDTO tenantDTO = tenantService.selectByType(TenantTypeEnum.FANTAI.getType());
        BillProfitSharingSnapshotDTO fantaiBillProfitSharingSnapShotDto = null;
        BigDecimal residuePrice = deliveryFee;
        Long tenantId = orderDTO.getTenantId();

        if(DeliveryTypeEnum.BRAND_DELIVERY.getCode().equals(billProfitSharingSnapshotDTO.getDeliveryType())){
            for(BillProfitSharingSnapshotDTO billProfitSharingSnapshotDto:billProfitSharingSnapshotDtos){
                if (!tenantId.equals(billProfitSharingSnapshotDto.getAccountId())) {
                    billProfitSharingSnapshotDto.setOriginPrice(deliveryFee);
                    BigDecimal profitSharingPrice = NumberUtil.mul(deliveryFee, NumberUtil.div(billProfitSharingSnapshotDto.getNumber(), NumberConstant.HUNDRED)).setScale(NumberConstant.TWO, ROUND_HALF_UP);
                    residuePrice = NumberUtil.sub(residuePrice, profitSharingPrice);
                    billProfitSharingSnapshotDto.setProfitSharingPrice(profitSharingPrice);
                    billProfitSharingService.updateBillProfitSharingSnapshot(billProfitSharingSnapshotDto);
                }else {
                    fantaiBillProfitSharingSnapShotDto = billProfitSharingSnapshotDto;
                }
            }
        }else {
            // 供应商
            TenantDTO supplierTenant = tenantService.selectByType(TenantTypeEnum.SUPPLIER.getType());
            if(deliveryFee.compareTo(BigDecimal.ZERO) != NumberConstant.ZERO) {
                // 运费退回比例 退款/运费 * 供应商
                BigDecimal rate = NumberUtil.div(refundDeliveryFee, deliveryFee);
                supplierDeliveryFee = NumberUtil.mul(supplierDeliveryFee, NumberUtil.sub(BigDecimal.ONE, rate)).setScale(NumberConstant.TWO, ROUND_HALF_UP);
            }
            // 计算供应商实际运费
            // 三方仓
            for(BillProfitSharingSnapshotDTO billProfitSharingSnapshotDto:billProfitSharingSnapshotDtos){
                // 供应商
                if(supplierTenant.getId().equals(billProfitSharingSnapshotDto.getAccountId())){
                    billProfitSharingSnapshotDto.setOriginPrice(deliveryFee);
                    billProfitSharingSnapshotDto.setProfitSharingPrice(supplierDeliveryFee);
                    residuePrice = NumberUtil.sub(residuePrice, supplierDeliveryFee);
                    billProfitSharingService.updateBillProfitSharingSnapshot(billProfitSharingSnapshotDto);
                }else if(tenantDTO.getId().equals(billProfitSharingSnapshotDto.getAccountId())){
                    // 帆台
                    fantaiBillProfitSharingSnapShotDto = billProfitSharingSnapshotDto;
                }else {
                    // 品牌方
                    BigDecimal brandDeliveryFee = NumberUtil.sub(deliveryFee, supplierDeliveryFee);
                    billProfitSharingSnapshotDto.setOriginPrice(deliveryFee);
                    billProfitSharingSnapshotDto.setProfitSharingPrice(brandDeliveryFee);
                    residuePrice = NumberUtil.sub(residuePrice, brandDeliveryFee);
                    billProfitSharingService.updateBillProfitSharingSnapshot(billProfitSharingSnapshotDto);
                }
            }
        }
//        AssertCheckDefault.expectNotNull(fantaiBillProfitSharingSnapShotDto, "帆台分账明细不存在:" + JSON.toJSONString(billProfitSharingSnapshotDtos));
        // 帆台
        fantaiBillProfitSharingSnapShotDto.setOriginPrice(deliveryFee);
        fantaiBillProfitSharingSnapShotDto.setProfitSharingPrice(residuePrice);
        billProfitSharingService.updateBillProfitSharingSnapshot(fantaiBillProfitSharingSnapShotDto);
    }

    /**
     * 无仓运费分账计算
     * 运费 - 运费退款直接分给品牌方
     *
     * @param billProfitSharingSnapshotDtos
     * @param orderDTO
     * @param afterSaleDTOList
     */
    private void noWarehouseDeliveryFeeProfitSharingCalculate(List<BillProfitSharingSnapshotDTO> billProfitSharingSnapshotDtos, OrderResp orderDTO, List<OrderAfterSaleResp> afterSaleDTOList) {
        BigDecimal deliveryFee = Objects.isNull(orderDTO.getDeliveryFee()) ? BigDecimal.ZERO : orderDTO.getDeliveryFee();
        BigDecimal totalDeliveryRefundFee = afterSaleDTOList.stream().filter(el -> Objects.nonNull(el.getDeliveryFee())).map(OrderAfterSaleResp::getDeliveryFee).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal remainingPrice = NumberUtil.sub(deliveryFee, totalDeliveryRefundFee);
        for (BillProfitSharingSnapshotDTO billProfitSharingSnapshotDto : billProfitSharingSnapshotDtos) {
            billProfitSharingSnapshotDto.setOriginPrice(remainingPrice);
            BigDecimal profitSharingPrice = Objects.equals(billProfitSharingSnapshotDto.getAccountType(), AccountTypeEnum.TENANT.getType()) ? remainingPrice : BigDecimal.ZERO;
            billProfitSharingSnapshotDto.setProfitSharingPrice(profitSharingPrice);
            billProfitSharingService.updateBillProfitSharingSnapshot(billProfitSharingSnapshotDto);
        }
    }
}
